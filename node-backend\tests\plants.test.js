/**
 * 植物CRUD API测试
 * 测试植物数据的增删改查功能
 */

const request = require('supertest');
const User = require('../src/models/User');

describe('植物CRUD API', () => {
  let app;
  let authToken;
  let testUserId;

  beforeAll(async () => {
    try {
      app = require('../src/app');
      User.clearMockUsers();
      
      // 注册测试用户并获取认证令牌
      const userData = {
        username: 'plantuser',
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123'
      };

      const registerResponse = await request(app)
        .post('/api/v1/auth/register')
        .send(userData);

      if (registerResponse.status === 201) {
        authToken = registerResponse.body.data.token;
        testUserId = registerResponse.body.data.user.id;
      }
    } catch (error) {
      console.log('App not found - this is expected for TDD red phase');
    }
  });

  beforeEach(() => {
    // 每个测试前清除植物模拟数据
    if (process.env.NODE_ENV === 'test') {
      // 这里会在实现Plant模型后添加清除逻辑
    }
  });

  afterAll(async () => {
    if (app && app.close) {
      await app.close();
    }
  });

  describe('创建植物 POST /api/v1/plants', () => {
    test('应该成功创建新植物', async () => {
      if (!app || !authToken) {
        expect(true).toBe(false); // TDD红色阶段
        return;
      }

      const plantData = {
        name: '绿萝',
        species: 'Epipremnum aureum',
        description: '一种常见的室内观叶植物',
        location: '客厅',
        acquiredDate: '2024-01-15',
        image: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k='
      };

      const response = await request(app)
        .post('/api/v1/plants')
        .set('Authorization', `Bearer ${authToken}`)
        .send(plantData)
        .expect(201);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('plant');
      expect(response.body.data.plant).toHaveProperty('id');
      expect(response.body.data.plant).toHaveProperty('name', '绿萝');
      expect(response.body.data.plant).toHaveProperty('species', 'Epipremnum aureum');
      expect(response.body.data.plant).toHaveProperty('userId', testUserId);
    });

    test('应该拒绝未认证的请求', async () => {
      if (!app) {
        expect(true).toBe(false);
        return;
      }

      const plantData = {
        name: '绿萝',
        species: 'Epipremnum aureum'
      };

      const response = await request(app)
        .post('/api/v1/plants')
        .send(plantData)
        .expect(401);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error.message).toContain('未提供认证令牌');
    });

    test('应该验证必填字段', async () => {
      if (!app || !authToken) {
        expect(true).toBe(false);
        return;
      }

      const response = await request(app)
        .post('/api/v1/plants')
        .set('Authorization', `Bearer ${authToken}`)
        .send({})
        .expect(400);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toHaveProperty('message');
    });
  });

  describe('获取植物列表 GET /api/v1/plants', () => {
    test('应该返回用户的植物列表', async () => {
      if (!app || !authToken) {
        expect(true).toBe(false);
        return;
      }

      // 先创建一个植物
      const plantData = {
        name: '吊兰',
        species: 'Chlorophytum comosum',
        description: '净化空气的植物',
        location: '阳台'
      };

      await request(app)
        .post('/api/v1/plants')
        .set('Authorization', `Bearer ${authToken}`)
        .send(plantData)
        .expect(201);

      // 获取植物列表
      const response = await request(app)
        .get('/api/v1/plants')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('plants');
      expect(Array.isArray(response.body.data.plants)).toBe(true);
      expect(response.body.data.plants.length).toBeGreaterThan(0);
    });

    test('应该支持分页查询', async () => {
      if (!app || !authToken) {
        expect(true).toBe(false);
        return;
      }

      const response = await request(app)
        .get('/api/v1/plants?page=1&limit=10')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('plants');
      expect(response.body.data).toHaveProperty('pagination');
      expect(response.body.data.pagination).toHaveProperty('page', 1);
      expect(response.body.data.pagination).toHaveProperty('limit', 10);
    });

    test('应该支持按名称搜索', async () => {
      if (!app || !authToken) {
        expect(true).toBe(false);
        return;
      }

      const response = await request(app)
        .get('/api/v1/plants?search=绿萝')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('plants');
    });
  });

  describe('获取单个植物 GET /api/v1/plants/:id', () => {
    test('应该返回指定植物的详细信息', async () => {
      if (!app || !authToken) {
        expect(true).toBe(false);
        return;
      }

      // 先创建一个植物
      const plantData = {
        name: '仙人掌',
        species: 'Cactaceae',
        description: '耐旱植物',
        location: '窗台'
      };

      const createResponse = await request(app)
        .post('/api/v1/plants')
        .set('Authorization', `Bearer ${authToken}`)
        .send(plantData)
        .expect(201);

      const plantId = createResponse.body.data.plant.id;

      // 获取植物详情
      const response = await request(app)
        .get(`/api/v1/plants/${plantId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('plant');
      expect(response.body.data.plant).toHaveProperty('id', plantId);
      expect(response.body.data.plant).toHaveProperty('name', '仙人掌');
    });

    test('应该拒绝访问其他用户的植物', async () => {
      if (!app || !authToken) {
        expect(true).toBe(false);
        return;
      }

      const response = await request(app)
        .get('/api/v1/plants/999')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error.message).toContain('植物不存在');
    });
  });

  describe('更新植物 PUT /api/v1/plants/:id', () => {
    test('应该成功更新植物信息', async () => {
      if (!app || !authToken) {
        expect(true).toBe(false);
        return;
      }

      // 先创建一个植物
      const plantData = {
        name: '芦荟',
        species: 'Aloe vera',
        description: '药用植物',
        location: '卧室'
      };

      const createResponse = await request(app)
        .post('/api/v1/plants')
        .set('Authorization', `Bearer ${authToken}`)
        .send(plantData)
        .expect(201);

      const plantId = createResponse.body.data.plant.id;

      // 更新植物信息
      const updateData = {
        name: '芦荟（更新）',
        description: '具有药用价值的多肉植物',
        location: '阳台'
      };

      const response = await request(app)
        .put(`/api/v1/plants/${plantId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data.plant).toHaveProperty('name', '芦荟（更新）');
      expect(response.body.data.plant).toHaveProperty('description', '具有药用价值的多肉植物');
    });
  });

  describe('删除植物 DELETE /api/v1/plants/:id', () => {
    test('应该成功删除植物', async () => {
      if (!app || !authToken) {
        expect(true).toBe(false);
        return;
      }

      // 先创建一个植物
      const plantData = {
        name: '待删除植物',
        species: 'Test Plant',
        description: '用于测试删除功能',
        location: '测试位置'
      };

      const createResponse = await request(app)
        .post('/api/v1/plants')
        .set('Authorization', `Bearer ${authToken}`)
        .send(plantData)
        .expect(201);

      const plantId = createResponse.body.data.plant.id;

      // 删除植物
      const response = await request(app)
        .delete(`/api/v1/plants/${plantId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message');

      // 验证植物已被删除
      await request(app)
        .get(`/api/v1/plants/${plantId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });
  });
});
