/**
 * 输入验证中间件
 * 使用Joi进行请求数据验证
 */

const Jo<PERSON> = require('joi');

/**
 * 创建验证中间件
 */
const validate = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false, // 返回所有错误
      stripUnknown: true // 移除未知字段
    });

    if (error) {
      const errorMessages = error.details.map(detail => detail.message);
      return res.status(400).json({
        error: {
          message: errorMessages.join('; '),
          status: 400,
          details: errorMessages
        }
      });
    }

    // 将验证后的数据替换原始数据
    req.body = value;
    next();
  };
};

/**
 * 用户注册验证规则
 */
const registerValidation = Joi.object({
  username: Joi.string()
    .alphanum()
    .min(3)
    .max(30)
    .required()
    .messages({
      'string.alphanum': '用户名只能包含字母和数字',
      'string.min': '用户名至少需要3个字符',
      'string.max': '用户名不能超过30个字符',
      'any.required': '用户名是必填项'
    }),

  email: Joi.string()
    .email()
    .required()
    .messages({
      'string.email': '请输入有效的邮箱地址',
      'any.required': '邮箱是必填项'
    }),

  password: Joi.string()
    .min(6)
    .max(128)
    .required()
    .messages({
      'string.min': '密码至少需要6个字符',
      'string.max': '密码不能超过128个字符',
      'any.required': '密码是必填项'
    }),

  confirmPassword: Joi.string()
    .valid(Joi.ref('password'))
    .required()
    .messages({
      'any.only': '密码确认不匹配',
      'any.required': '密码确认是必填项'
    })
});

/**
 * 用户登录验证规则
 */
const loginValidation = Joi.object({
  email: Joi.string()
    .email()
    .required()
    .messages({
      'string.email': '请输入有效的邮箱地址',
      'any.required': '邮箱是必填项'
    }),

  password: Joi.string()
    .required()
    .messages({
      'any.required': '密码是必填项'
    })
});

/**
 * 刷新令牌验证规则
 */
const refreshTokenValidation = Joi.object({
  refreshToken: Joi.string()
    .required()
    .messages({
      'any.required': '刷新令牌是必填项'
    })
});

/**
 * 密码重置请求验证规则
 */
const passwordResetRequestValidation = Joi.object({
  email: Joi.string()
    .email()
    .required()
    .messages({
      'string.email': '请输入有效的邮箱地址',
      'any.required': '邮箱是必填项'
    })
});

/**
 * 密码重置验证规则
 */
const passwordResetValidation = Joi.object({
  token: Joi.string()
    .required()
    .messages({
      'any.required': '重置令牌是必填项'
    }),

  password: Joi.string()
    .min(6)
    .max(128)
    .required()
    .messages({
      'string.min': '密码至少需要6个字符',
      'string.max': '密码不能超过128个字符',
      'any.required': '密码是必填项'
    }),

  confirmPassword: Joi.string()
    .valid(Joi.ref('password'))
    .required()
    .messages({
      'any.only': '密码确认不匹配',
      'any.required': '密码确认是必填项'
    })
});

/**
 * 植物创建验证规则
 */
const plantCreateValidation = Joi.object({
  name: Joi.string()
    .min(1)
    .max(100)
    .required()
    .messages({
      'string.min': '植物名称不能为空',
      'string.max': '植物名称不能超过100个字符',
      'any.required': '植物名称是必填项'
    }),

  species: Joi.string()
    .max(200)
    .allow('')
    .messages({
      'string.max': '植物种类不能超过200个字符'
    }),

  description: Joi.string()
    .max(1000)
    .allow('')
    .messages({
      'string.max': '植物描述不能超过1000个字符'
    }),

  location: Joi.string()
    .max(100)
    .allow('')
    .messages({
      'string.max': '植物位置不能超过100个字符'
    }),

  acquiredDate: Joi.date()
    .iso()
    .max('now')
    .messages({
      'date.format': '获得日期格式不正确',
      'date.max': '获得日期不能是未来时间'
    }),

  image: Joi.string()
    .max(10485760) // 10MB base64 limit
    .allow('')
    .messages({
      'string.max': '图片数据过大，请压缩后重试'
    })
});

/**
 * 植物更新验证规则
 */
const plantUpdateValidation = Joi.object({
  name: Joi.string()
    .min(1)
    .max(100)
    .messages({
      'string.min': '植物名称不能为空',
      'string.max': '植物名称不能超过100个字符'
    }),

  species: Joi.string()
    .max(200)
    .allow('')
    .messages({
      'string.max': '植物种类不能超过200个字符'
    }),

  description: Joi.string()
    .max(1000)
    .allow('')
    .messages({
      'string.max': '植物描述不能超过1000个字符'
    }),

  location: Joi.string()
    .max(100)
    .allow('')
    .messages({
      'string.max': '植物位置不能超过100个字符'
    }),

  acquiredDate: Joi.date()
    .iso()
    .max('now')
    .messages({
      'date.format': '获得日期格式不正确',
      'date.max': '获得日期不能是未来时间'
    }),

  image: Joi.string()
    .max(10485760)
    .allow('')
    .messages({
      'string.max': '图片数据过大，请压缩后重试'
    })
});

/**
 * 护理记录创建验证规则
 */
const careRecordCreateValidation = Joi.object({
  plantId: Joi.number()
    .integer()
    .positive()
    .required()
    .messages({
      'number.base': '植物ID必须是数字',
      'number.integer': '植物ID必须是整数',
      'number.positive': '植物ID必须是正数',
      'any.required': '植物ID是必填项'
    }),

  type: Joi.string()
    .valid('watering', 'fertilizing', 'pruning', 'repotting', 'pest_control', 'cleaning', 'observation', 'other')
    .required()
    .messages({
      'any.only': '护理类型无效',
      'any.required': '护理类型是必填项'
    }),

  description: Joi.string()
    .max(500)
    .allow('')
    .messages({
      'string.max': '护理描述不能超过500个字符'
    }),

  amount: Joi.string()
    .max(100)
    .allow('')
    .messages({
      'string.max': '护理用量不能超过100个字符'
    }),

  notes: Joi.string()
    .max(1000)
    .allow('')
    .messages({
      'string.max': '护理备注不能超过1000个字符'
    }),

  careDate: Joi.date()
    .iso()
    .max('now')
    .messages({
      'date.format': '护理日期格式不正确',
      'date.max': '护理日期不能是未来时间'
    })
});

/**
 * 护理记录更新验证规则
 */
const careRecordUpdateValidation = Joi.object({
  type: Joi.string()
    .valid('watering', 'fertilizing', 'pruning', 'repotting', 'pest_control', 'cleaning', 'observation', 'other')
    .messages({
      'any.only': '护理类型无效'
    }),

  description: Joi.string()
    .max(500)
    .allow('')
    .messages({
      'string.max': '护理描述不能超过500个字符'
    }),

  amount: Joi.string()
    .max(100)
    .allow('')
    .messages({
      'string.max': '护理用量不能超过100个字符'
    }),

  notes: Joi.string()
    .max(1000)
    .allow('')
    .messages({
      'string.max': '护理备注不能超过1000个字符'
    }),

  careDate: Joi.date()
    .iso()
    .max('now')
    .messages({
      'date.format': '护理日期格式不正确',
      'date.max': '护理日期不能是未来时间'
    })
});

module.exports = {
  validate,
  registerValidation,
  loginValidation,
  refreshTokenValidation,
  passwordResetRequestValidation,
  passwordResetValidation,
  plantCreateValidation,
  plantUpdateValidation,
  careRecordCreateValidation,
  careRecordUpdateValidation
};
