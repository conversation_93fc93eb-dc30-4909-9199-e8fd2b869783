/**
 * 植物识别路由
 *
 * 定义植物识别相关的API端点
 */

const express = require('express');
const multer = require('multer');
const { body, query } = require('express-validator');
const { authenticateToken: auth } = require('../middlewares/auth');
const identificationController = require('../controllers/identificationController');

const router = express.Router();

// 配置multer用于文件上传
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
    files: 1
  },
  fileFilter: (req, file, cb) => {
    // 只允许图片文件
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'), false);
    }
  }
});

// 验证规则
const identifyValidation = [
  body('textDescription')
    .optional()
    .isString()
    .isLength({ min: 1, max: 2000 })
    .withMessage('Text description must be between 1 and 2000 characters'),

  body('molecularData')
    .optional()
    .isString()
    .isLength({ min: 1, max: 50000 })
    .withMessage('Molecular data must be between 1 and 50000 characters'),

  body('imageBase64')
    .optional()
    .isString()
    .withMessage('Image data must be a valid base64 string'),

  body('options.model')
    .optional()
    .isString()
    .withMessage('Model must be a string'),

  body('options.temperature')
    .optional()
    .isFloat({ min: 0, max: 2 })
    .withMessage('Temperature must be between 0 and 2')
];

const careAdviceValidation = [
  body('plantName')
    .notEmpty()
    .isString()
    .isLength({ min: 1, max: 200 })
    .withMessage('Plant name is required and must be between 1 and 200 characters'),

  body('conditions.location')
    .optional()
    .isString()
    .isLength({ max: 100 })
    .withMessage('Location must be less than 100 characters'),

  body('conditions.climate')
    .optional()
    .isString()
    .isLength({ max: 100 })
    .withMessage('Climate must be less than 100 characters'),

  body('conditions.season')
    .optional()
    .isString()
    .isIn(['spring', 'summer', 'autumn', 'winter'])
    .withMessage('Season must be one of: spring, summer, autumn, winter')
];

const batchIdentifyValidation = [
  body('items')
    .isArray({ min: 1, max: 10 })
    .withMessage('Items must be an array with 1-10 elements'),

  body('items.*.textDescription')
    .optional()
    .isString()
    .isLength({ min: 1, max: 2000 })
    .withMessage('Text description must be between 1 and 2000 characters'),

  body('items.*.molecularData')
    .optional()
    .isString()
    .isLength({ min: 1, max: 50000 })
    .withMessage('Molecular data must be between 1 and 50000 characters'),

  body('items.*.imageBase64')
    .optional()
    .isString()
    .withMessage('Image data must be a valid base64 string')
];

const historyValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),

  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),

  query('startDate')
    .optional()
    .isISO8601()
    .withMessage('Start date must be a valid ISO 8601 date'),

  query('endDate')
    .optional()
    .isISO8601()
    .withMessage('End date must be a valid ISO 8601 date')
];

/**
 * @route   POST /api/identification/identify
 * @desc    多模态植物识别
 * @access  Public (可选认证)
 */
router.post('/identify',
  upload.single('image'),
  identifyValidation,
  identificationController.identifyPlant
);

/**
 * @route   POST /api/identification/care-advice
 * @desc    获取植物护理建议
 * @access  Public
 */
router.post('/care-advice',
  careAdviceValidation,
  identificationController.getCareAdvice
);

/**
 * @route   POST /api/identification/batch-identify
 * @desc    批量植物识别
 * @access  Private
 */
router.post('/batch-identify',
  auth,
  batchIdentifyValidation,
  identificationController.batchIdentify
);

/**
 * @route   GET /api/identification/history
 * @desc    获取用户识别历史
 * @access  Private
 */
router.get('/history',
  auth,
  historyValidation,
  identificationController.getIdentificationHistory
);

/**
 * @route   GET /api/identification/service-status
 * @desc    检查OpenRouter服务状态
 * @access  Public
 */
router.get('/service-status',
  identificationController.getServiceStatus
);

// 错误处理中间件
router.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        message: 'File too large. Maximum size is 10MB.'
      });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        success: false,
        message: 'Too many files. Only one file is allowed.'
      });
    }
  }

  if (error.message === 'Only image files are allowed') {
    return res.status(400).json({
      success: false,
      message: 'Only image files are allowed.'
    });
  }

  next(error);
});

module.exports = router;
