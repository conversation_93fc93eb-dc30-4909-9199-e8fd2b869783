/**
 * PlantApp Backend API 主应用
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');

const config = require('./config');
const databaseManager = require('./config/database');
const { notFoundHandler, errorHandler, logger } = require('./middlewares/errorHandler');
const healthRoutes = require('./routes/health');
const authRoutes = require('./routes/auth');
const plantRoutes = require('./routes/plants');
const careRecordRoutes = require('./routes/careRecords');
const growthRecordRoutes = require('./routes/growthRecords');
const reminderRoutes = require('./routes/reminders');

class PlantAppServer {
  constructor() {
    this.app = express();
    this.server = null;
    this.setupMiddlewares();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  /**
   * 设置中间件
   */
  setupMiddlewares() {
    // 安全中间件
    this.app.use(helmet({
      contentSecurityPolicy: false, // 暂时禁用CSP以便开发
    }));

    // CORS配置
    this.app.use(cors({
      origin: process.env.NODE_ENV === 'test' ? true : config.cors.origin,
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
      exposedHeaders: ['X-Total-Count']
    }));

    // 压缩响应
    this.app.use(compression());

    // 请求日志
    this.app.use(morgan('combined', {
      stream: {
        write: (message) => logger.info(message.trim())
      }
    }));

    // 解析JSON请求体
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // 速率限制
    const limiter = rateLimit({
      windowMs: config.rateLimit.windowMs,
      max: config.rateLimit.maxRequests,
      message: {
        error: {
          message: '请求过于频繁，请稍后重试',
          status: 429
        }
      },
      standardHeaders: true,
      legacyHeaders: false
    });
    this.app.use('/api/', limiter);

    // 信任代理（用于获取真实IP）
    this.app.set('trust proxy', 1);
  }

  /**
   * 设置路由
   */
  setupRoutes() {
    // 基础健康检查路由
    this.app.use('/health', healthRoutes);

    // API v1 路由
    this.app.get('/api/v1/health', async (req, res, next) => {
      // 直接调用详细健康检查
      req.url = '/detailed';
      healthRoutes(req, res, next);
    });

    // 认证路由
    this.app.use('/api/v1/auth', authRoutes);

    // 植物路由
    this.app.use('/api/v1/plants', plantRoutes);

    // 护理记录路由
    this.app.use('/api/v1/care-records', careRecordRoutes);

    // 生长记录路由
    this.app.use('/api/v1/growth-records', growthRecordRoutes);

    // 提醒路由
    this.app.use('/api/v1/reminders', reminderRoutes);

    // API根路径信息
    this.app.get('/api', (req, res) => {
      res.json({
        name: 'PlantApp Backend API',
        version: '1.0.0',
        description: '植物识别与护理应用后端服务',
        endpoints: {
          health: '/health',
          api_v1: '/api/v1'
        }
      });
    });

    // 根路径
    this.app.get('/', (req, res) => {
      res.json({
        message: '欢迎使用PlantApp Backend API',
        version: '1.0.0',
        documentation: '/api',
        health: '/health'
      });
    });

    // 测试路由（用于测试JSON解析错误）
    this.app.post('/api/v1/test', (req, res) => {
      res.json({ message: 'Test endpoint', data: req.body });
    });
  }

  /**
   * 设置错误处理
   */
  setupErrorHandling() {
    // 404处理
    this.app.use(notFoundHandler);

    // 全局错误处理
    this.app.use(errorHandler);
  }

  /**
   * 启动服务器
   */
  async start() {
    try {
      // 初始化数据库连接（可选，测试环境可能没有数据库）
      if (config.server.env !== 'test') {
        try {
          await databaseManager.initPostgreSQL();
          await databaseManager.initRedis();
        } catch (error) {
          logger.warn('数据库连接失败，但服务器将继续启动:', error.message);
        }
      }

      // 启动HTTP服务器
      this.server = this.app.listen(config.server.port, config.server.host, () => {
        logger.info(`🚀 PlantApp Backend API 启动成功`);
        logger.info(`📍 服务地址: http://${config.server.host}:${config.server.port}`);
        logger.info(`🌍 运行环境: ${config.server.env}`);
        logger.info(`📊 健康检查: http://${config.server.host}:${config.server.port}/health`);
      });

      // 优雅关闭处理
      process.on('SIGTERM', () => this.gracefulShutdown('SIGTERM'));
      process.on('SIGINT', () => this.gracefulShutdown('SIGINT'));

    } catch (error) {
      logger.error('服务器启动失败:', error);
      process.exit(1);
    }
  }

  /**
   * 优雅关闭服务器
   */
  async gracefulShutdown(signal) {
    logger.info(`收到 ${signal} 信号，开始优雅关闭...`);

    if (this.server) {
      this.server.close(async () => {
        logger.info('HTTP服务器已关闭');

        // 关闭数据库连接
        await databaseManager.close();

        logger.info('应用已完全关闭');
        process.exit(0);
      });
    }
  }

  /**
   * 关闭服务器（用于测试）
   */
  async close() {
    if (this.server) {
      return new Promise((resolve) => {
        this.server.close(resolve);
      });
    }
  }

  /**
   * 获取Express应用实例
   */
  getApp() {
    return this.app;
  }
}

// 创建应用实例
const plantAppServer = new PlantAppServer();

// 如果直接运行此文件，启动服务器
if (require.main === module) {
  plantAppServer.start();
}

// 导出应用实例供测试使用
module.exports = plantAppServer.getApp();
